import { NextRequest, NextResponse } from 'next/server'
import { writeFile } from 'fs/promises'
import path from 'path'
import { validateImage, generateFileName, generateSocialFormats } from '@/lib/image-utils'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('image') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      )
    }
    
    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    // Validate image
    const validation = await validateImage(buffer)
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      )
    }
    
    // Generate unique filename
    const fileName = generateFileName(file.name)
    const uploadPath = path.join(process.cwd(), 'public', 'uploads', fileName)
    
    // Save original file
    await writeFile(uploadPath, buffer)
    
    // Generate all social media formats
    const socialFormats = await generateSocialFormats(buffer)
    const formatUrls: Record<string, string> = {}
    
    // Save each format
    for (const format of socialFormats) {
      const formatFileName = generateFileName(file.name, format.name)
      const formatPath = path.join(process.cwd(), 'public', 'uploads', formatFileName)
      await writeFile(formatPath, format.buffer)
      formatUrls[format.name] = `/uploads/${formatFileName}`
    }
    
    const originalUrl = `/uploads/${fileName}`
    
    return NextResponse.json({
      success: true,
      originalUrl,
      metadata: validation.metadata,
      formats: formatUrls,
    })
    
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Upload failed' },
      { status: 500 }
    )
  }
}
