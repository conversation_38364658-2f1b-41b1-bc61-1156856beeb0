[build]
  publish = ".next"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[functions]
  node_bundler = "esbuild"

# Increase timeout for AI functions
[[functions]]
  function = "src/app/api/ai-image/route.ts"
  timeout = 30

[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Headers = "Content-Type"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"

[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/uploads/*"  
  [headers.values]
    Cache-Control = "public, max-age=86400"

[dev]
  command = "npm run dev"
  port = 3000

# Redirect rules
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"], Country = ["US"]}

# Environment variables (will be set in Netlify dashboard)
# GOOGLE_GEMINI_API_KEY - required for AI functionality
