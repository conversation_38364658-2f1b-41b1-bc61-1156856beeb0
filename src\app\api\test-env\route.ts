import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'

export async function GET(request: NextRequest) {
  try {
    const apiKey = process.env.GOOGLE_GEMINI_API_KEY

    let geminiTest = null
    if (apiKey) {
      try {
        const genAI = new GoogleGenerativeAI(apiKey)
        const model = genAI.getGenerativeModel({ model: "models/gemini-2.5-flash-image-preview" })
        const result = await model.generateContent("Test message")
        geminiTest = {
          success: true,
          modelVersion: result.response.modelVersion || 'unknown',
          responseLength: result.response.text()?.length || 0
        }
      } catch (geminiError: any) {
        geminiTest = {
          success: false,
          error: geminiError.message,
          errorCode: geminiError.code || 'unknown'
        }
      }
    }

    return NextResponse.json({
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey ? apiKey.length : 0,
      apiKeyPrefix: apiKey ? apiKey.substring(0, 10) + '...' : 'not set',
      environment: process.env.NODE_ENV,
      allEnvKeys: Object.keys(process.env).filter(key => key.includes('GEMINI') || key.includes('GOOGLE')),
      geminiTest
    })
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to check environment', details: error.message },
      { status: 500 }
    )
  }
}
