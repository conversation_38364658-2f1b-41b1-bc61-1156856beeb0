import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Social media format dimensions
export const SOCIAL_FORMATS = {
  facebook: {
    post: { width: 1200, height: 630 },
    square: { width: 1080, height: 1080 },
    story: { width: 1080, height: 1920 },
  },
  instagram: {
    square: { width: 1080, height: 1080 },
    portrait: { width: 1080, height: 1350 },
    story: { width: 1080, height: 1920 },
  },
} as const

export type SocialPlatform = keyof typeof SOCIAL_FORMATS
export type FormatType<T extends SocialPlatform> = keyof typeof SOCIAL_FORMATS[T]
