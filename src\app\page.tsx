import Link from "next/link"
import { <PERSON><PERSON><PERSON>, Image, <PERSON>rk<PERSON>, Share2 } from "lucide-react"

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
              <h1 className="text-xl font-bold">Socky Format Generator</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="flex-1">
        <section className="py-20 text-center">
          <div className="container mx-auto px-4 max-w-4xl">
            <div className="mb-8">
              <h2 className="text-4xl md:text-6xl font-bold tracking-tight mb-6">
                Vytvořte perfektní obsah pro{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  sociální sítě
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                AI-powered nástroj pro generování, formátování a optimalizaci obsahu 
                pro Facebook a Instagram. Nahrajte obrázek nebo ho nechte vygenerovat AI, 
                naformátuje se automaticky a text se optimalizuje pro maximum dosahu.
              </p>
            </div>

            <div className="mb-12">
              <Link 
                href="/editor"
                className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-8 py-4 rounded-lg text-lg font-medium hover:bg-primary/90 transition-colors"
              >
                Začít vytvářet
                <ArrowRight className="w-5 h-5" />
              </Link>
            </div>

            {/* Features Grid */}
            <div className="grid md:grid-cols-3 gap-8 mt-16">
              <div className="p-6 rounded-lg border bg-card">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  <Image className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Upload nebo AI generování</h3>
                <p className="text-muted-foreground">
                  Nahrajte vlastní obrázek nebo využijte Google Gemini 2.5 Flash s Nano Banana modelem pro generování jedinečných vizuálů.
                </p>
              </div>

              <div className="p-6 rounded-lg border bg-card">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  <Sparkles className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Automatické formátování</h3>
                <p className="text-muted-foreground">
                  Obrázky se automaticky naformátují do správných rozměrů pro Facebook posty, Instagram square/portrait a Stories.
                </p>
              </div>

              <div className="p-6 rounded-lg border bg-card">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  <Share2 className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">AI optimalizace textů</h3>
                <p className="text-muted-foreground">
                  Texty se upraví pro maximální dosah - gramatické opravy, klíčová slova, trendy hashtags a virální elementy.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t py-8">
        <div className="container mx-auto px-4 text-center text-muted-foreground">
          <p>© 2024 Socky Format Generator. Vytvořeno s AI pomocí Claude a Google Gemini.</p>
        </div>
      </footer>
    </div>
  )
}
