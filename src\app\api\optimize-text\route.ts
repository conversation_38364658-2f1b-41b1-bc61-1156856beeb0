import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'

// Initialize Google AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY!)

const OPTIMIZATION_PROMPT = `You are a social media copywriting expert. Your task is to improve the following text while:

1. PRESERVING the original idea and message - this is most important!
2. Making it sound professional and engaging
3. Suitable for Facebook and Instagram
4. Fixing grammar errors
5. Adding emotional elements and engagement
6. Keeping the personal tone

DO NOT completely rewrite - just improve what's already there!

For HASHTAGS:
- Create a MIX of keywords from text + popular viral hashtags
- Use 15-20 hashtags
- Combine: specific keywords + trending tags (#viral #trending #instagood #photooftheday #motivation #inspiration #lifestyle #love #follow #instadaily #reels #explore)
- Add Czech hashtags if text is in Czech (#cesko #czechrepublic #czech)

IMPORTANT: Return response ONLY as valid JSON object without any additional comments or text. Start directly with { and end with }.

Response format:
{
  "optimizedText": "improved text without hashtags that preserves original idea but sounds better",
  "hashtags": ["#keyword1", "#keyword2", "#viral", "#trending", "#instagood"],
  "improvements": ["what was improved"]
}

Original text to improve:
`

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json()
    
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return NextResponse.json(
        { error: 'Valid text is required' },
        { status: 400 }
      )
    }
    
    if (!process.env.GOOGLE_GEMINI_API_KEY) {
      return NextResponse.json(
        { error: 'Google Gemini API key not configured' },
        { status: 500 }
      )
    }
    
    try {
      const model = genAI.getGenerativeModel({ 
        model: "gemini-2.5-flash",
        generationConfig: {
          temperature: 0.3,
          topP: 0.95,
          topK: 64,
          maxOutputTokens: 2048,
        },
      })
      
      const fullPrompt = OPTIMIZATION_PROMPT + text
      
      const result = await model.generateContent(fullPrompt)
      const response = result.response
      const optimizedContent = response.text()
      
      // console.log('Gemini response received:', optimizedContent.length, 'characters')
      
      // Try to parse JSON response
      let parsedResult
      try {
        // Clean the response text to extract JSON (handle markdown code blocks)
        let cleanContent = optimizedContent
          .replace(/```json\s*/g, '') // Remove ```json
          .replace(/```\s*$/g, '')    // Remove closing ```
          .trim()
        
        // Try to find JSON object
        const jsonMatch = cleanContent.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          parsedResult = JSON.parse(jsonMatch[0])
        } else {
          throw new Error('No JSON found in response')
        }
      } catch (parseError) {
        console.error('Failed to parse AI response as JSON:', parseError)
        
        // Fallback: create a basic optimized version
        parsedResult = {
          optimizedText: optimizedContent.replace(/```json|```/g, '').trim(),
          hashtags: [
            "#socialmedia", "#content", "#instagram", "#facebook", 
            "#marketing", "#viral", "#trendy", "#awesome"
          ],
          improvements: ["Výsledek byl automaticky zpracován"]
        }
      }
      
      // Validate the result structure
      if (!parsedResult.optimizedText) {
        parsedResult.optimizedText = text // fallback to original
      }
      if (!Array.isArray(parsedResult.hashtags)) {
        parsedResult.hashtags = ["#content", "#socialmedia"]
      }
      if (!Array.isArray(parsedResult.improvements)) {
        parsedResult.improvements = ["Text byl optimalizován pro social media"]
      }
      
      // Ensure hashtags have # prefix
      parsedResult.hashtags = parsedResult.hashtags.map((tag: string) => 
        tag.startsWith('#') ? tag : `#${tag}`
      )
      
      return NextResponse.json({
        success: true,
        original: text,
        optimized: parsedResult.optimizedText,
        hashtags: parsedResult.hashtags,
        improvements: parsedResult.improvements,
        fullOptimizedText: parsedResult.optimizedText + '\n\n' + parsedResult.hashtags.join(' ')
      })
      
    } catch (genError) {
      console.error('Gemini API error:', genError)
      
      // Create a simple enhancement of the original text as fallback
      const simpleOptimizedText = text.charAt(0).toUpperCase() + text.slice(1) + 
        (text.endsWith('.') || text.endsWith('!') || text.endsWith('?') ? '' : '.')
      
      // Generate smart hashtag combination
      const words = text.toLowerCase().split(/\s+/).filter(w => w.length > 3)
      
      // Popular viral hashtags for maximum reach
      const viralHashtags = [
        '#viral', '#trending', '#instagood', '#photooftheday', 
        '#motivation', '#inspiration', '#lifestyle', '#love',
        '#follow', '#instadaily', '#reels', '#explore', '#fyp'
      ]
      
      // Czech hashtags if text contains Czech words
      const czechWords = ['je', 'to', 'se', 'na', 'v', 'a', 'jsem', 'byl', 'byla', 'nebo', 'tak', 'už', 'ale', 'když']
      const isCzech = words.some(w => czechWords.includes(w))
      const czechHashtags = isCzech ? ['#cesko', '#czechrepublic', '#czech'] : []
      
      // Context-based hashtags from content
      const contextHashtags = []
      
      // Photo/visual content
      if (words.some(w => ['foto', 'obrázek', 'image', 'picture', 'fotografie', 'fotka'].includes(w))) {
        contextHashtags.push('#photography', '#photo', '#picture', '#visual')
      }
      
      // Business content
      if (words.some(w => ['business', 'firma', 'práce', 'work', 'podnikání', 'kariéra'].includes(w))) {
        contextHashtags.push('#business', '#work', '#entrepreneur', '#success')
      }
      
      // Fashion/style content
      if (words.some(w => ['móda', 'fashion', 'style', 'outfit', 'oblečení'].includes(w))) {
        contextHashtags.push('#fashion', '#style', '#outfit', '#ootd')
      }
      
      // Food content
      if (words.some(w => ['jídlo', 'food', 'recept', 'vaření', 'restaurace', 'káva', 'coffee'].includes(w))) {
        contextHashtags.push('#food', '#foodie', '#recipe', '#delicious', '#yummy')
      }
      
      // Travel content
      if (words.some(w => ['cestování', 'travel', 'dovolená', 'výlet', 'trip', 'vacation'].includes(w))) {
        contextHashtags.push('#travel', '#vacation', '#trip', '#wanderlust', '#adventure')
      }
      
      // Fitness/health content
      if (words.some(w => ['fitness', 'sport', 'zdraví', 'cvičení', 'workout', 'gym'].includes(w))) {
        contextHashtags.push('#fitness', '#workout', '#health', '#gym', '#fitlife')
      }
      
      // Extract potential keywords from text (longer words that could be hashtags)
      const keywordHashtags = words
        .filter(word => word.length >= 4 && word.length <= 15)
        .filter(word => /^[a-záčďéěíňóřšťúůýžA-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/.test(word))
        .slice(0, 3)
        .map(word => `#${word.toLowerCase()}`)
      
      // Combine all hashtags: viral + context + keywords + czech
      const allHashtags = [
        ...viralHashtags.slice(0, 8), // Top 8 viral
        ...contextHashtags.slice(0, 4), // Up to 4 context
        ...keywordHashtags, // Keywords from text
        ...czechHashtags // Czech tags if applicable
      ]
      
      // Remove duplicates and limit to 18 hashtags
      const fallbackHashtags = [...new Set(allHashtags)].slice(0, 18)
      
      return NextResponse.json({
        success: true,
        original: text,
        optimized: simpleOptimizedText,
        hashtags: fallbackHashtags, // Use all generated hashtags (up to 18)
        improvements: ["Text byl mírně upraven + přidány virální hashtags (záložní režim)"],
        fullOptimizedText: simpleOptimizedText + '\n\n' + fallbackHashtags.join(' '),
        note: "Použit záložní režim s virálními hashtags - AI optimalizace není momentálně dostupná."
      })
    }
    
  } catch (error) {
    console.error('Text optimization error:', error)
    return NextResponse.json(
      { error: 'Failed to optimize text' },
      { status: 500 }
    )
  }
}
