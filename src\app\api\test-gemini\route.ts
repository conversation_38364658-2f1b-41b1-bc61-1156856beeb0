import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'

export async function GET(request: NextRequest) {
  try {
    const apiKey = process.env.GOOGLE_GEMINI_API_KEY
    
    if (!apiKey) {
      return NextResponse.json({ error: 'No API key' }, { status: 500 })
    }

    const genAI = new GoogleGenerativeAI(apiKey)
    
    // Test basic model first
    try {
      const basicModel = genAI.getGenerativeModel({ model: "gemini-2.5-flash" })
      const basicResult = await basicModel.generateContent("Hello")
      
      // Test image preview model
      try {
        const imageModel = genAI.getGenerativeModel({ model: "models/gemini-2.5-flash-image-preview" })
        const imageResult = await imageModel.generateContent("Generate a red circle")
        
        return NextResponse.json({
          success: true,
          basicModel: {
            success: true,
            modelVersion: basicResult.response.modelVersion,
            textLength: basicResult.response.text().length
          },
          imageModel: {
            success: true,
            modelVersion: imageResult.response.modelVersion,
            textLength: imageResult.response.text().length
          }
        })
      } catch (imageError: any) {
        return NextResponse.json({
          success: false,
          basicModel: {
            success: true,
            modelVersion: basicResult.response.modelVersion,
            textLength: basicResult.response.text().length
          },
          imageModel: {
            success: false,
            error: imageError.message,
            code: imageError.code
          }
        })
      }
    } catch (basicError: any) {
      return NextResponse.json({
        success: false,
        basicModel: {
          success: false,
          error: basicError.message,
          code: basicError.code
        },
        imageModel: null
      })
    }
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to test Gemini', details: error.message },
      { status: 500 }
    )
  }
}
