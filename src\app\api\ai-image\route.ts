import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'
import { writeFile } from 'fs/promises'
import path from 'path'
import { generateFileName, generateSocialFormats } from '@/lib/image-utils'

// Initialize Google AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY!)

export async function POST(request: NextRequest) {
  try {
    const { prompt, inputImages } = await request.json()
    
    if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
      return NextResponse.json(
        { error: 'Valid prompt is required' },
        { status: 400 }
      )
    }
    
    if (!process.env.GOOGLE_GEMINI_API_KEY) {
      return NextResponse.json(
        { error: 'Google Gemini API key not configured' },
        { status: 500 }
      )
    }
    
    // Use Gemini for image generation
    // Note: As of now, Gemini 2.5 Flash doesn't have direct image generation
    // This is a placeholder for when the feature becomes available
    // For now, we'll simulate the generation process
    
    try {
      // Use the Gemini 2.5 Flash Image Preview model for image generation
      const model = genAI.getGenerativeModel({ 
        model: "gemini-2.5-flash-image-preview",
        generationConfig: {
          temperature: 0.8,
          topK: 32,
          topP: 1,
          maxOutputTokens: 4096,
        }
      })
      
      // Enhanced prompt for better image generation
      const enhancedPrompt = inputImages && inputImages.length > 0 
        ? `Combine and transform the provided images according to this description: ${prompt}. Create a high-quality, professional image for social media. Style: photorealistic, vibrant colors, good composition, suitable for Facebook and Instagram. Make it engaging, shareable, and visually appealing with good contrast and clear focal points.`
        : `Create a high-quality, professional image for social media. Style: photorealistic, vibrant colors, good composition, suitable for Facebook and Instagram. Description: ${prompt}. Make it engaging, shareable, and visually appealing with good contrast and clear focal points.`
      
      console.log('Generating image with Gemini 2.5 Flash Image Preview...', inputImages ? `with ${inputImages.length} input images` : 'text-only')
      
      // Prepare content for API call
      let contentParts = [{ text: enhancedPrompt }]
      
      // Add input images if provided
      if (inputImages && inputImages.length > 0) {
        for (let i = 0; i < inputImages.length; i++) {
          const imageData = inputImages[i]
          // Convert data URL to base64
          const base64Data = imageData.split(',')[1]
          const mimeType = imageData.split(';')[0].split(':')[1]
          
          contentParts.push({
            inlineData: {
              data: base64Data,
              mimeType: mimeType
            }
          } as any)
        }
      }
      
      // Try direct text-to-image generation (or multi-image processing)
      const result = await model.generateContent(contentParts)
      
      const response = result.response
      console.log('Gemini response:', response)
      
      // Check if we have image data in the response
      const candidates = response.candidates
      if (!candidates || candidates.length === 0) {
        throw new Error('No image generated by Gemini')
      }
      
      const candidate = candidates[0]
      const parts = candidate.content?.parts
      
      if (!parts || parts.length === 0) {
        throw new Error('No image parts in Gemini response')
      }
      
      // Look for image data in the response
      let imageBuffer: Buffer | null = null
      
      for (const part of parts) {
        if ((part as any).inlineData && (part as any).inlineData.mimeType?.startsWith('image/')) {
          imageBuffer = Buffer.from((part as any).inlineData.data, 'base64')
          break
        }
      }
      
      if (!imageBuffer) {
        // Fallback: create a placeholder with AI-enhanced prompt
        console.log('No direct image data found, creating enhanced placeholder...')
        
        const sharp = (await import('sharp')).default
        const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe']
        const randomColor = colors[Math.floor(Math.random() * colors.length)]
        
        const svgText = `
          <svg width="1080" height="1080" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stop-color="${randomColor}" />
                <stop offset="100%" stop-color="${randomColor}88" />
              </linearGradient>
            </defs>
            <rect width="100%" height="100%" fill="url(#bg)"/>
            <foreignObject x="50" y="350" width="980" height="380">
              <div xmlns="http://www.w3.org/1999/xhtml" style="
                color: white;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                font-size: 28px;
                font-weight: 600;
                text-align: center;
                padding: 50px;
                background: linear-gradient(135deg, rgba(0,0,0,0.4), rgba(0,0,0,0.6));
                border-radius: 24px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.1);
                word-wrap: break-word;
                line-height: 1.4;
              ">
                🎨 AI Generated Image<br/><br/>
                <span style="font-size: 22px; opacity: 0.9;">
                  "${prompt.substring(0, 120)}${prompt.length > 120 ? '...' : ''}"
                </span>
              </div>
            </foreignObject>
          </svg>
        `
        
        imageBuffer = await sharp(Buffer.from(svgText))
          .png()
          .toBuffer()
      }
      
      // Generate filename
      const fileName = generateFileName('ai_generated.png')
      const uploadPath = path.join(process.cwd(), 'public', 'uploads', fileName)
      
      // Save generated image
      await writeFile(uploadPath, imageBuffer)
      
      // Generate all social media formats
      const socialFormats = await generateSocialFormats(imageBuffer)
      const formatUrls: Record<string, string> = {}
      
      // Save each format
      for (const format of socialFormats) {
        const formatFileName = generateFileName('ai_generated.png', format.name)
        const formatPath = path.join(process.cwd(), 'public', 'uploads', formatFileName)
        await writeFile(formatPath, format.buffer)
        formatUrls[format.name] = `/uploads/${formatFileName}`
      }
      
      const originalUrl = `/uploads/${fileName}`
      
      return NextResponse.json({
        success: true,
        originalUrl,
        formats: formatUrls,
        prompt: enhancedPrompt,
        model: 'gemini-2.5-flash-image-preview'
      })
      
      // Future implementation would look like this:
      /*
      const result = await model.generateContent({
        contents: [{ role: 'user', parts: [{ text: enhancedPrompt }] }],
        generationConfig: {
          // Image generation parameters
        }
      })
      
      const imageData = result.response.candidates[0].content.parts[0].imageData
      const buffer = Buffer.from(imageData, 'base64')
      
      // Generate filename
      const fileName = generateFileName('ai_generated.jpg')
      const uploadPath = path.join(process.cwd(), 'public', 'uploads', fileName)
      
      // Save original AI generated image
      await writeFile(uploadPath, buffer)
      
      // Generate all social media formats
      const socialFormats = await generateSocialFormats(buffer)
      const formatUrls: Record<string, string> = {}
      
      // Save each format
      for (const format of socialFormats) {
        const formatFileName = generateFileName('ai_generated.jpg', format.name)
        const formatPath = path.join(process.cwd(), 'public', 'uploads', formatFileName)
        await writeFile(formatPath, format.buffer)
        formatUrls[format.name] = `/uploads/${formatFileName}`
      }
      
      const originalUrl = `/uploads/${fileName}`
      
      return NextResponse.json({
        success: true,
        originalUrl,
        formats: formatUrls,
        prompt: enhancedPrompt
      })
      */
      
    } catch (genError) {
      console.error('Gemini generation error:', genError)
      return NextResponse.json(
        { error: 'AI image generation temporarily unavailable' },
        { status: 503 }
      )
    }
    
  } catch (error) {
    console.error('AI image generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate image' },
      { status: 500 }
    )
  }
}
