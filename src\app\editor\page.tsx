"use client"

import React, { useState, useCallback } from 'react'
import { Upload } from 'lucide-react'
import { <PERSON><PERSON><PERSON> } from 'lucide-react'
import { Image as ImageIcon } from 'lucide-react'
import { Facebook } from 'lucide-react'
import { Instagram } from 'lucide-react'
import { Wand2 } from 'lucide-react'
import { Download } from 'lucide-react'
import { Copy } from 'lucide-react'
import { ArrowLeft } from 'lucide-react'
import { Edit } from 'lucide-react'
import ImageCropEditor from '@/components/ImageCropEditor'
import MultiImageGallery from '@/components/MultiImageGallery'
import { type PixelCrop } from 'react-image-crop'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'

type SocialFormat = 'facebook-post' | 'facebook-square' | 'facebook-story' | 'instagram-square' | 'instagram-portrait' | 'instagram-story'

const SOCIAL_FORMATS = {
  'facebook-post': { name: 'Facebook Post', dimensions: '1200×630', aspect: 'aspect-[1200/630]' },
  'facebook-square': { name: 'Facebook Čtvercový', dimensions: '1080×1080', aspect: 'aspect-square' },
  'facebook-story': { name: 'Facebook Story', dimensions: '1080×1920', aspect: 'aspect-[9/16]' },
  'instagram-square': { name: 'Instagram Čtvercový', dimensions: '1080×1080', aspect: 'aspect-square' },
  'instagram-portrait': { name: 'Instagram Portrét', dimensions: '1080×1350', aspect: 'aspect-[4/5]' },
  'instagram-story': { name: 'Instagram Story', dimensions: '1080×1920', aspect: 'aspect-[9/16]' },
} as const

export default function EditorPage() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [originalImage, setOriginalImage] = useState<string | null>(null) // Store original uncropped image
  const [aiPrompt, setAiPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedFormat, setSelectedFormat] = useState<SocialFormat>('instagram-square')
  const [caption, setCaption] = useState('')
  const [isOptimizing, setIsOptimizing] = useState(false)
  
  // Crop editor state
  const [cropEditorOpen, setCropEditorOpen] = useState(false)
  const [currentCrop, setCurrentCrop] = useState<PixelCrop | null>(null)
  
  // Multi-image state
  const [multiImages, setMultiImages] = useState<string[]>([])
  
  // Helper function to get aspect ratio for current format
  const getAspectRatio = (format: SocialFormat): number => {
    const formatData = SOCIAL_FORMATS[format]
    const dimensions = formatData.dimensions.split('×')
    return parseInt(dimensions[0]) / parseInt(dimensions[1])
  }
  
  // Generate cropped preview
  const generateCroppedPreview = useCallback((imageUrl: string, crop: PixelCrop): Promise<string> => {
    return new Promise((resolve) => {
      const image = new Image()
      image.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        if (!ctx) {
          resolve(imageUrl)
          return
        }
        
        canvas.width = crop.width
        canvas.height = crop.height
        
        ctx.drawImage(
          image,
          crop.x,
          crop.y,
          crop.width,
          crop.height,
          0,
          0,
          crop.width,
          crop.height
        )
        
        resolve(canvas.toDataURL('image/jpeg', 0.95))
      }
      image.src = imageUrl
    })
  }, [])
  
  // Handle crop save
  const handleCropSave = useCallback(async (cropData: PixelCrop) => {
    setCurrentCrop(cropData)
    console.log('Crop saved for format:', selectedFormat, cropData)
    
    // Generate cropped preview
    if (originalImage || selectedImage) {
      const sourceImage = originalImage || selectedImage!
      try {
        const croppedPreview = await generateCroppedPreview(sourceImage, cropData)
        setSelectedImage(croppedPreview)
        
        // Store original if not already stored
        if (!originalImage) {
          setOriginalImage(sourceImage)
        }
      } catch (error) {
        console.error('Failed to generate cropped preview:', error)
      }
    }
  }, [selectedFormat, selectedImage, originalImage, generateCroppedPreview])
  
  // Clear crop
  const handleClearCrop = useCallback(() => {
    setCurrentCrop(null)
    
    // Restore original image if available
    if (originalImage) {
      setSelectedImage(originalImage)
    }
    
    console.log('Crop cleared')
  }, [originalImage])
  
  const downloadImage = async () => {
    if (!selectedImage) return
    
    try {
      // Prepare request data with crop if available
      const requestData = {
        imageUrl: originalImage || selectedImage, // Use original image for processing
        format: selectedFormat,
        crop: currentCrop
      }
      
      // Download the image in the selected format
      const response = await fetch('/api/resize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })
      
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.resizedUrl) {
          // Download the resized image
          const imageResponse = await fetch(result.resizedUrl)
          const blob = await imageResponse.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `${selectedFormat}-image.jpg`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        } else {
          alert('Stahování se nezdařilo')
        }
      } else {
        alert('Stahování se nezdařilo')
      }
    } catch (error) {
      console.error('Download error:', error)
      alert('Nastala chyba při stahování')
    }
  }
  
  const copyTextToClipboard = async () => {
    if (!caption.trim()) return
    
    try {
      await navigator.clipboard.writeText(caption)
      alert('✅ Text byl zkopírován do schránky!')
    } catch (error) {
      console.error('Copy error:', error)
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = caption
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert('✅ Text byl zkopírován do schránky!')
    }
  }

  const handleImageUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Show preview immediately
      const reader = new FileReader()
      reader.onload = (e) => {
        const newImage = e.target?.result as string
        setSelectedImage(newImage)
        setOriginalImage(null) // Reset original image
        setCurrentCrop(null) // Reset crop when new image is loaded
      }
      reader.readAsDataURL(file)
      
      // Upload to server
      try {
        const formData = new FormData()
        formData.append('image', file)
        
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData
        })
        
        if (response.ok) {
          const result = await response.json()
          console.log('Upload successful:', result)
          // You could set the server URL here instead of the data URL
          // setSelectedImage(result.originalUrl)
        } else {
          console.error('Upload failed:', await response.text())
        }
      } catch (error) {
        console.error('Upload error:', error)
      }
    }
  }, [])

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const newImage = e.target?.result as string
        setSelectedImage(newImage)
        setOriginalImage(null) // Reset original image
        setCurrentCrop(null) // Reset crop when new image is loaded
      }
      reader.readAsDataURL(file)
    }
  }, [])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  const generateAIImage = async () => {
    if (!aiPrompt.trim()) return
    
    setIsGenerating(true)
    try {
      const response = await fetch('/api/ai-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          prompt: aiPrompt,
          inputImages: multiImages.length > 0 ? multiImages : undefined
        })
      })
      
      const result = await response.json()
      
      if (response.ok && result.success) {
        setSelectedImage(result.originalUrl)
        setOriginalImage(null) // Reset original image
        setCurrentCrop(null) // Reset crop for new AI generated image
        console.log('AI image generated:', result)
      } else {
        console.error('AI generation failed:', result.error)
        // Show user-friendly error message
        alert(result.error || 'Generování obrázku se nezdařilo')
      }
    } catch (error) {
      console.error('Error generating image:', error)
      alert('Nastala chyba při generování obrázku')
    } finally {
      setIsGenerating(false)
    }
  }

  const optimizeText = async () => {
    if (!caption.trim()) return
    
    setIsOptimizing(true)
    try {
      const response = await fetch('/api/optimize-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text: caption })
      })
      
      const result = await response.json()
      
      if (response.ok && result.success) {
        setCaption(result.fullOptimizedText)
        console.log('Text optimized:', result)
        
        // Text was successfully optimized - no popup needed
        console.log('Text optimized successfully:', result)
      } else {
        console.error('Text optimization failed:', result.error)
        alert(result.error || 'Optimalizace textu se nezdařila')
      }
    } catch (error) {
      console.error('Error optimizing text:', error)
      alert('Nastala chyba při optimalizaci textu')
    } finally {
      setIsOptimizing(false)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b sticky top-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link 
                href="/"
                className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground"
              >
                <ArrowLeft className="w-4 h-4" />
                Zpět na hlavní stránku
              </Link>
              <div className="h-6 w-px bg-border" />
              <h1 className="text-xl font-semibold">Editor</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-12rem)]">
          
          {/* Panel 1: Image Upload/Generation */}
          <div className="space-y-4">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ImageIcon className="w-5 h-5" />
                  Obrázek
                </CardTitle>
                <CardDescription>
                  Nahrajte obrázek nebo ho nechte vygenerovat AI
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                
                {/* Upload Area */}
                <div
                  className={cn(
                    "border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
                    selectedImage ? "border-green-300 bg-green-50" : "border-gray-300 hover:border-gray-400 hover:bg-gray-50"
                  )}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onClick={() => {
                    const input = document.getElementById('image-upload') as HTMLInputElement
                    if (input) {
                      input.click()
                    }
                  }}
                >
                  {selectedImage ? (
                    <div className="space-y-2">
                      <img 
                        src={selectedImage} 
                        alt="Uploaded" 
                        className="max-w-full max-h-32 mx-auto rounded"
                      />
                      <p className="text-sm text-green-600">✅ Obrázek nahrán</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="w-8 h-8 mx-auto text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">
                        Klikněte zde nebo přetáhněte obrázek
                      </p>
                    </div>
                  )}
                </div>

                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                  ref={(input) => {
                    if (input) {
                      (window as any).imageUploadInput = input
                    }
                  }}
                />
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => {
                    const input = document.getElementById('image-upload') as HTMLInputElement
                    if (input) {
                      input.click()
                    }
                  }}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Nahrát obrázek
                </Button>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Nebo
                    </span>
                  </div>
                </div>

                {/* Multi-Image Upload for AI */}
                <div className="space-y-3">
                  <div className="text-sm font-medium">Přidejte obrázky pro kombinaci (volitelné)</div>
                  <MultiImageGallery 
                    images={multiImages}
                    onImagesChange={setMultiImages}
                    maxImages={3}
                  />
                </div>
                
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Text prompt
                    </span>
                  </div>
                </div>

                {/* AI Generation */}
                <div className="space-y-3">
                  <Textarea
                    placeholder={multiImages.length > 0 ? 
                      "Popište, jak chcete kombinovat nahrané obrázky..." : 
                      "Popište, jaký obrázek chcete vygenerovat..."
                    }
                    value={aiPrompt}
                    onChange={(e) => setAiPrompt(e.target.value)}
                    className="min-h-[100px]"
                  />
                  <Button 
                    onClick={generateAIImage}
                    disabled={!aiPrompt.trim() || isGenerating}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                        Generuji...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        {multiImages.length > 0 ? 'Kombinovat s AI' : 'Generovat s AI'}
                      </>
                    )}
                  </Button>
                </div>

              </CardContent>
            </Card>
          </div>

          {/* Panel 2: Format Selection & Preview */}
          <div className="space-y-4">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Facebook className="w-5 h-5 text-blue-600" />
                  <Instagram className="w-5 h-5 text-pink-600" />
                  Formát
                </CardTitle>
                <CardDescription>
                  Vyberte formát pro sociální síť
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                
                {/* Format Buttons */}
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(SOCIAL_FORMATS).map(([key, format]) => (
                    <Button
                      key={key}
                      variant={selectedFormat === key ? "default" : "outline"}
                      onClick={() => setSelectedFormat(key as SocialFormat)}
                      className="flex flex-col h-auto p-3 text-xs"
                    >
                      <span className="font-medium">{format.name}</span>
                      <span className="text-muted-foreground">{format.dimensions}</span>
                    </Button>
                  ))}
                </div>

                {/* Preview */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Náhled</h4>
                  <div className="bg-muted rounded-lg p-4 flex items-center justify-center min-h-[200px]">
                    {selectedImage ? (
                      <div className={cn("bg-white rounded shadow-sm max-w-full", SOCIAL_FORMATS[selectedFormat].aspect, "w-32")}>
                        <img 
                          src={selectedImage}
                          alt="Preview"
                          className="w-full h-full object-cover rounded"
                        />
                      </div>
                    ) : (
                      <div className={cn("bg-gray-200 border-2 border-dashed border-gray-300 rounded flex items-center justify-center", SOCIAL_FORMATS[selectedFormat].aspect, "w-32")}>
                        <ImageIcon className="w-8 h-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground text-center">
                    {SOCIAL_FORMATS[selectedFormat].name} • {SOCIAL_FORMATS[selectedFormat].dimensions}
                  </p>
                </div>

                {/* Export Buttons */}
                <div className="space-y-2">
                  <Button 
                    variant={currentCrop ? "default" : "outline"}
                    className="w-full" 
                    disabled={!selectedImage}
                    onClick={() => setCropEditorOpen(true)}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    {currentCrop ? 'Oříznutí nastaveno' : 'Upravit oříznutí'}
                  </Button>
                  
                  {currentCrop && (
                    <Button 
                      variant="ghost"
                      size="sm"
                      className="w-full text-xs" 
                      onClick={handleClearCrop}
                    >
                      Zrušit oříznutí
                    </Button>
                  )}
                  
                  <Button 
                    variant="outline" 
                    className="w-full" 
                    disabled={!selectedImage}
                    onClick={downloadImage}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Stáhnout obrázek
                  </Button>
                </div>

              </CardContent>
            </Card>
          </div>

          {/* Panel 3: Text Editor */}
          <div className="space-y-4">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wand2 className="w-5 h-5" />
                  Popisek
                </CardTitle>
                <CardDescription>
                  Napište svůj text a nechte AI ho vylepšit - zachová vaši myšlenku
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                
                <Textarea
                  placeholder="Napište svou myšlenku, text nebo příspěvek... AI to vylepší a udělá poutavější"
                  value={caption}
                  onChange={(e) => setCaption(e.target.value)}
                  className="min-h-[200px]"
                />

                <Button
                  onClick={optimizeText}
                  disabled={!caption.trim() || isOptimizing}
                  className="w-full"
                >
                  {isOptimizing ? (
                    <>
                      <Wand2 className="w-4 h-4 mr-2 animate-pulse" />
                      Optimalizuji...
                    </>
                    ) : (
                      <>
                        <Wand2 className="w-4 h-4 mr-2" />
                        Vylepšit s AI
                      </>
                    )}
                </Button>

                <div className="text-xs text-muted-foreground">
                  <p>💡 AI zachová vaši myšlenku, ale udělá text profesionálnější, poutavější a přidá relevantní hashtags</p>
                </div>

                <div className="space-y-2">
                  <Button 
                    variant="outline" 
                    className="w-full" 
                    disabled={!caption.trim()}
                    onClick={copyTextToClipboard}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Zkopírovat text
                  </Button>
                </div>

              </CardContent>
            </Card>
          </div>

        </div>
      </main>
      
      {/* Crop Editor Modal */}
      <ImageCropEditor
        imageUrl={originalImage || selectedImage || ''}
        isOpen={cropEditorOpen}
        onClose={() => setCropEditorOpen(false)}
        onSave={handleCropSave}
        aspectRatio={getAspectRatio(selectedFormat)}
        formatName={SOCIAL_FORMATS[selectedFormat].name}
        existingCrop={currentCrop}
      />
    </div>
  )
}
