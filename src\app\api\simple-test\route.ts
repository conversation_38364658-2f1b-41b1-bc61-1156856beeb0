import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const apiKey = process.env.GOOGLE_GEMINI_API_KEY
    
    if (!apiKey) {
      return NextResponse.json({ 
        error: 'No API key found',
        duration: Date.now() - startTime
      }, { status: 500 })
    }

    console.log('Starting Gemini test...')
    const genAI = new GoogleGenerativeAI(apiKey)
    
    try {
      console.log('Creating model...')
      const model = genAI.getGenerativeModel({ 
        model: "models/gemini-2.5-flash-image-preview",
        generationConfig: {
          temperature: 0.8,
          maxOutputTokens: 100, // Reduced for faster response
        }
      })
      
      console.log('Calling generateContent...')
      const result = await model.generateContent("Hello")
      console.log('Got result:', result.response.modelVersion)
      
      return NextResponse.json({
        success: true,
        modelVersion: result.response.modelVersion,
        textLength: result.response.text().length,
        duration: Date.now() - startTime,
        environment: process.env.NODE_ENV
      })
      
    } catch (modelError: any) {
      console.error('Model error:', modelError)
      return NextResponse.json({
        success: false,
        error: modelError.message,
        code: modelError.code,
        stack: modelError.stack?.split('\n').slice(0, 3),
        duration: Date.now() - startTime,
        environment: process.env.NODE_ENV
      }, { status: 500 })
    }
    
  } catch (error: any) {
    console.error('General error:', error)
    return NextResponse.json({
      error: 'Failed to test',
      details: error.message,
      duration: Date.now() - startTime,
      environment: process.env.NODE_ENV
    }, { status: 500 })
  }
}
