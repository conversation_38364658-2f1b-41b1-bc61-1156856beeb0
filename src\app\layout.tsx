import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Socky Format Generator",
  description: "AI-powered social media content formatter for Facebook and Instagram",
  keywords: ["social media", "content generation", "AI", "Facebook", "Instagram"],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="cs" className="light">
      <body className={inter.className}>
        <div className="min-h-screen bg-background">
          {children}
        </div>
      </body>
    </html>
  )
}
