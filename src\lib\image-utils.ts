import sharp from 'sharp'
import { SOCIAL_FORMATS, type SocialPlatform, type FormatType } from './utils'

export interface ResizeOptions {
  width: number
  height: number
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside'
  position?: string
}

export async function resizeImage(
  inputBuffer: Buffer, 
  options: ResizeOptions
): Promise<Buffer> {
  try {
    const resized = sharp(inputBuffer)
      .resize(options.width, options.height, {
        fit: options.fit || 'cover',
        position: options.position || 'center'
      })
      .jpeg({ quality: 90 })
      .toBuffer()
    
    return resized
  } catch (error) {
    throw new Error(`Image resizing failed: ${error}`)
  }
}

export function getSocialFormatDimensions(platform: SocialPlatform, formatType: string) {
  const format = SOCIAL_FORMATS[platform]
  if (!format || !(formatType in format)) {
    throw new Error(`Invalid format: ${platform}/${formatType}`)
  }
  
  return format[formatType as FormatType<typeof platform>]
}

export async function generateSocialFormats(inputBuffer: Buffer) {
  const formats = [
    { name: 'facebook-post', ...SOCIAL_FORMATS.facebook.post },
    { name: 'facebook-square', ...SOCIAL_FORMATS.facebook.square },
    { name: 'facebook-story', ...SOCIAL_FORMATS.facebook.story },
    { name: 'instagram-square', ...SOCIAL_FORMATS.instagram.square },
    { name: 'instagram-portrait', ...SOCIAL_FORMATS.instagram.portrait },
    { name: 'instagram-story', ...SOCIAL_FORMATS.instagram.story },
  ]
  
  const results: Array<{ name: string; buffer: Buffer; width: number; height: number }> = []
  
  for (const format of formats) {
    try {
      const resized = await resizeImage(inputBuffer, {
        width: format.width,
        height: format.height,
        fit: 'cover'
      })
      
      results.push({
        name: format.name,
        buffer: resized,
        width: format.width,
        height: format.height
      })
    } catch (error) {
      console.error(`Failed to resize to ${format.name}:`, error)
    }
  }
  
  return results
}

export function generateFileName(originalName: string, suffix?: string): string {
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 8)
  const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg'
  
  const baseName = originalName.replace(/\.[^/.]+$/, "").replace(/[^a-zA-Z0-9]/g, '_')
  const finalSuffix = suffix ? `_${suffix}` : ''
  
  return `${baseName}_${timestamp}_${randomString}${finalSuffix}.${extension}`
}

export async function validateImage(buffer: Buffer): Promise<{ isValid: boolean; metadata?: sharp.Metadata; error?: string }> {
  try {
    const metadata = await sharp(buffer).metadata()
    
    if (!metadata.width || !metadata.height) {
      return { isValid: false, error: 'Invalid image dimensions' }
    }
    
    if (metadata.width > 4096 || metadata.height > 4096) {
      return { isValid: false, error: 'Image too large (max 4096x4096)' }
    }
    
    const allowedFormats = ['jpeg', 'png', 'webp', 'gif']
    if (!metadata.format || !allowedFormats.includes(metadata.format)) {
      return { isValid: false, error: 'Unsupported image format' }
    }
    
    return { isValid: true, metadata }
  } catch (error) {
    return { isValid: false, error: `Image validation failed: ${error}` }
  }
}
