import { NextRequest, NextResponse } from 'next/server'
import { readFile, writeFile } from 'fs/promises'
import path from 'path'
import { resizeImage, generateFileName, getSocialFormatDimensions } from '@/lib/image-utils'

export async function POST(request: NextRequest) {
  try {
    const { imageUrl, format, crop } = await request.json()
    
    if (!imageUrl || !format) {
      return NextResponse.json(
        { error: 'Image URL and format are required' },
        { status: 400 }
      )
    }
    
    // Parse format (e.g., "facebook-post", "instagram-square")
    const [platform, formatType] = format.split('-')
    
    if (!platform || !formatType) {
      return NextResponse.json(
        { error: 'Invalid format. Use format like "facebook-post" or "instagram-square"' },
        { status: 400 }
      )
    }
    
    try {
      // Get dimensions for the requested format
      const dimensions = getSocialFormatDimensions(platform as any, formatType)
      
      // Read the original image
      const imagePath = path.join(process.cwd(), 'public', imageUrl.replace(/^\//, ''))
      let imageBuffer = await readFile(imagePath)
      
      // Apply crop if provided
      if (crop && crop.width && crop.height) {
        const sharp = (await import('sharp')).default
        imageBuffer = await sharp(imageBuffer)
          .extract({
            left: Math.round(crop.x),
            top: Math.round(crop.y),
            width: Math.round(crop.width),
            height: Math.round(crop.height)
          })
          .toBuffer()
      }
      
      // Resize the image
      const resizedBuffer = await resizeImage(imageBuffer, {
        width: dimensions.width,
        height: dimensions.height,
        fit: 'cover'
      })
      
      // Generate new filename
      const originalFilename = path.basename(imageUrl)
      const resizedFilename = generateFileName(originalFilename, format)
      const resizedPath = path.join(process.cwd(), 'public', 'uploads', resizedFilename)
      
      // Save resized image
      await writeFile(resizedPath, resizedBuffer)
      
      const resizedUrl = `/uploads/${resizedFilename}`
      
      return NextResponse.json({
        success: true,
        originalUrl: imageUrl,
        resizedUrl,
        format,
        dimensions
      })
      
    } catch (formatError) {
      console.error('Format/resize error:', formatError)
      return NextResponse.json(
        { error: 'Invalid format or resize failed' },
        { status: 400 }
      )
    }
    
  } catch (error) {
    console.error('Resize API error:', error)
    return NextResponse.json(
      { error: 'Resize operation failed' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const url = searchParams.get('url')
  const size = searchParams.get('size')
  
  if (!url || !size) {
    return NextResponse.json(
      { error: 'URL and size parameters are required' },
      { status: 400 }
    )
  }
  
  try {
    // This is for direct download/streaming of resized images
    const [platform, formatType] = size.split('-')
    
    if (!platform || !formatType) {
      return NextResponse.json(
        { error: 'Invalid size format' },
        { status: 400 }
      )
    }
    
    const dimensions = getSocialFormatDimensions(platform as any, formatType)
    const imagePath = path.join(process.cwd(), 'public', url.replace(/^\//, ''))
    const originalBuffer = await readFile(imagePath)
    
    const resizedBuffer = await resizeImage(originalBuffer, {
      width: dimensions.width,
      height: dimensions.height,
      fit: 'cover'
    })
    
    return new Response(resizedBuffer as unknown as BodyInit, {
      headers: {
        'Content-Type': 'image/jpeg',
        'Content-Disposition': `attachment; filename="${size}-${path.basename(url)}"`,
      },
    })
    
  } catch (error) {
    console.error('Direct resize error:', error)
    return NextResponse.json(
      { error: 'Failed to resize image' },
      { status: 500 }
    )
  }
}
