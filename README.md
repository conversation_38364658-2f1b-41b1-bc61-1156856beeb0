# 🎨 Socky Format Generator

AI-powered aplikace pro generování a optimalizaci obsahu pro sociální sítě (Facebook a Instagram).

## ✨ Funkce

### 🖼️ <PERSON><PERSON><PERSON><PERSON> s obrázky
- **Upload vlastních obr<PERSON>zk<PERSON>** - drag & drop nebo kliknutím
- **AI generování obrázků** - pomocí Google Gemini 2.5 Flash Image Preview
- **<PERSON>k<PERSON>** pro všechny sociální sítě:
  - Facebook: Post (1200×630), Čtvercový (1080×1080), Story (1080×1920)
  - Instagram: Čtvercový (1080×1080), <PERSON><PERSON>t (1080×1350), Story (1080×1920)
- **Stahování** optimalizovaných obrázků

### ✍️ AI optimalizace textů
- **Zachován<PERSON> p<PERSON> my<PERSON>** - AI pouze vylepš<PERSON>, nepřepíše
- **Profesionální <PERSON>pra<PERSON>** - gramatika, styl, poutavost
- **Smart hashtags** - komb<PERSON>ce klíčových slov z textu + virální hashtags
- **Virální elementy** - trending hashtags pro maximáln<PERSON> dosah
- **České hashtags** - automatické přidání při detekci českého textu

### 🚀 Technologie
- **Frontend**: Next.js 15, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API routes
- **AI**: Google Gemini 2.5 Flash (text) + Image Preview (obrázky)
- **Image processing**: Sharp
- **UI**: Responsivní design s dark/light mode

## 🛠️ Instalace a spuštění

### Požadavky
- Node.js 18+
- npm nebo yarn
- Google Gemini API klíč

### Postup
1. **Klonování projektu**
   ```bash
   git clone <repo-url>
   cd SockyFormatGenerator
   ```

2. **Instalace závislostí**
   ```bash
   npm install
   ```

3. **Nastavení environment proměnných**
   ```bash
   cp .env.local.example .env.local
   ```
   
   Vyplňte v `.env.local`:
   ```
   GOOGLE_GEMINI_API_KEY=your_api_key_here
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   UPLOAD_DIR=uploads
   ```

4. **Spuštění**
   ```bash
   npm run dev
   ```
   
   Aplikace běží na: http://localhost:3000

## 📖 Použití

1. **Hlavní stránka** - přehled funkcí a CTA
2. **Editor** (`/editor`) - hlavní pracovní prostředí:
   - Levý panel: Upload/AI generování obrázků
   - Střední panel: Výběr formátu + náhled
   - Pravý panel: Textový editor + AI optimalizace

### Workflow
1. Nahrajte obrázek nebo zadejte prompt pro AI generování
2. Vyberte formát sociální sítě
3. Napište váš text/myšlenku
4. Klikněte "Vylepšit s AI" pro optimalizaci
5. Stáhněte obrázek a zkopírujte text

## 🔧 API Endpoints

- `POST /api/upload` - upload obrázků
- `POST /api/ai-image` - AI generování obrázků  
- `POST /api/optimize-text` - AI optimalizace textů
- `GET /api/resize` - změna velikosti obrázků

## 🎯 Hashtag strategie

Aplikace používá inteligentní kombinaci:
- **Klíčová slova** z vašeho textu
- **Virální hashtags**: #viral, #trending, #instagood, #photooftheday
- **Kontextové hashtags** podle obsahu (food, travel, fashion, atd.)
- **České hashtags**: #cesko, #czechrepublic (při detekci českého textu)
- **Engagement hashtags**: #motivation, #inspiration, #lifestyle

## 📱 Podporované formáty

| Platforma | Formát | Rozměry |
|-----------|---------|---------|
| Facebook | Post | 1200×630 |
| Facebook | Čtvercový | 1080×1080 |
| Facebook | Story | 1080×1920 |
| Instagram | Čtvercový | 1080×1080 |
| Instagram | Portrét | 1080×1350 |
| Instagram | Story | 1080×1920 |

## 🚀 Deployment na Netlify

### 1. Příprava projektu
```bash
# Commitněte všechny změny
git add .
git commit -m "Ready for production deployment"
git push origin main
```

### 2. Nasazení na Netlify

#### Možnost A: Přes Netlify CLI
```bash
# Instalace Netlify CLI
npm install -g netlify-cli

# Login do Netlify
netlify login

# Deploy
netlify deploy --prod
```

#### Možnost B: Přes GitHub (doporučeno)
1. Pushněte projekt na GitHub
2. Jděte na [Netlify.com](https://netlify.com)
3. Klikněte "New site from Git"
4. Vyberte GitHub repository
5. Build settings budou automaticky detekované

### 3. Nastavení environment variables
V Netlify dashboardu:
1. Site settings → Environment variables
2. Přidejte: `GOOGLE_GEMINI_API_KEY` = `AIzaSyCSxw_iGpcHUT1ZQZZicwIJerZH_1h4u3Y`

### 4. Build nastavení (automatické)
- **Build command**: `npm run build`  
- **Publish directory**: `.next`
- **Node version**: 18
- **Functions**: Automaticky detekované API routes

### Vercel (alternativa)
```bash
npx vercel
```

## 🔒 Bezpečnost
- API klíče jsou zabezpečené přes environment variables
- Obrázky jsou validované před zpracováním
- Rate limiting pro API endpoints
- Sanitizace uživatelských vstupů

## 📄 Licence
MIT License - viz LICENSE soubor

## 🤝 Kontribuce
1. Fork projektu
2. Vytvoř feature branch
3. Commit změny
4. Push do branch
5. Otevři Pull Request

## 📞 Podpora
Pro issues a feature requesty použijte GitHub Issues.

---

Vytvořeno s ❤️ pomocí AI (Claude + Google Gemini) - v1.0
