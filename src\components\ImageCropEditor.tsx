"use client"

import React, { useState, useRef, useCallback } from 'react'
import ReactCrop, { 
  type Crop, 
  type PixelCrop,
  centerCrop,
  makeAspectCrop,
} from 'react-image-crop'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { X, Check, RotateCcw } from 'lucide-react'
import 'react-image-crop/dist/ReactCrop.css'

interface ImageCropEditorProps {
  imageUrl: string
  isOpen: boolean
  onClose: () => void
  onSave: (cropData: PixelCrop) => void
  aspectRatio: number
  formatName: string
  existingCrop?: PixelCrop | null
}

// This is to demonstrate how to make and center a % aspect crop
// which is a bit trickier so we use some helper functions.
function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number,
) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  )
}

export default function ImageCropEditor({
  imageUrl,
  isOpen,
  onClose,
  onSave,
  aspectRatio,
  formatName,
  existingCrop
}: ImageCropEditorProps) {
  const [crop, setCrop] = useState<Crop>()
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>()
  const imgRef = useRef<HTMLImageElement>(null)

  function onImageLoad(e: React.SyntheticEvent<HTMLImageElement>) {
    const image = e.currentTarget
    const displayWidth = image.width
    const displayHeight = image.height
    const naturalWidth = image.naturalWidth
    const naturalHeight = image.naturalHeight
    
    if (existingCrop && existingCrop.width && existingCrop.height) {
      // Convert natural crop coordinates to display coordinates
      const scaleX = displayWidth / naturalWidth
      const scaleY = displayHeight / naturalHeight
      
      const displayCrop: PixelCrop = {
        x: existingCrop.x * scaleX,
        y: existingCrop.y * scaleY,
        width: existingCrop.width * scaleX,
        height: existingCrop.height * scaleY,
        unit: 'px'
      }
      
      // Convert to percentage for ReactCrop
      const percentCrop: Crop = {
        unit: '%',
        x: (displayCrop.x / displayWidth) * 100,
        y: (displayCrop.y / displayHeight) * 100,
        width: (displayCrop.width / displayWidth) * 100,
        height: (displayCrop.height / displayHeight) * 100,
      }
      
      setCrop(percentCrop)
      setCompletedCrop(displayCrop)
    } else if (aspectRatio) {
      // Create new centered crop
      setCrop(centerAspectCrop(displayWidth, displayHeight, aspectRatio))
    }
  }

  const handleSave = useCallback(() => {
    if (completedCrop && imgRef.current) {
      const image = imgRef.current
      const displayWidth = image.width
      const displayHeight = image.height
      
      // Get natural (original) image size
      const naturalWidth = image.naturalWidth
      const naturalHeight = image.naturalHeight
      
      // Calculate scaling factors
      const scaleX = naturalWidth / displayWidth
      const scaleY = naturalHeight / displayHeight
      
      // Convert crop coordinates to natural image size
      const naturalCrop: PixelCrop = {
        x: completedCrop.x * scaleX,
        y: completedCrop.y * scaleY,
        width: completedCrop.width * scaleX,
        height: completedCrop.height * scaleY,
        unit: 'px'
      }
      
      console.log('Display crop:', completedCrop)
      console.log('Natural crop:', naturalCrop)
      console.log('Scale factors:', { scaleX, scaleY })
      console.log('Display size:', { displayWidth, displayHeight })
      console.log('Natural size:', { naturalWidth, naturalHeight })
      
      onSave(naturalCrop)
      onClose()
    }
  }, [completedCrop, onSave, onClose])

  const handleReset = useCallback(() => {
    if (imgRef.current && aspectRatio) {
      const { width, height } = imgRef.current
      setCrop(centerAspectCrop(width, height, aspectRatio))
    }
  }, [aspectRatio])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-lg font-semibold">
            Upravit oříznutí - {formatName}
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground">
            Přetáhněte rohy pro úpravu oříznutí. Oblast bude zachována při změně formátu.
          </div>
          
          <div className="flex justify-center overflow-auto max-h-[60vh]">
            <ReactCrop
              crop={crop}
              onChange={(_, percentCrop) => setCrop(percentCrop)}
              onComplete={(c) => setCompletedCrop(c)}
              aspect={aspectRatio}
              minWidth={50}
              minHeight={50}
              keepSelection
            >
              <img
                ref={imgRef}
                alt="Crop me"
                src={imageUrl}
                style={{ maxHeight: '60vh', maxWidth: '100%' }}
                onLoad={onImageLoad}
              />
            </ReactCrop>
          </div>
          
          <div className="flex justify-between items-center pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleReset}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Resetovat
            </Button>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={onClose}
              >
                Zrušit
              </Button>
              <Button
                onClick={handleSave}
                disabled={!completedCrop}
                className="flex items-center gap-2"
              >
                <Check className="h-4 w-4" />
                Uložit oříznutí
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
